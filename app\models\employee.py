from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, DateTime, ForeignKey, Date, Numeric, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from app.core.database import Base

class Employee(Base):
    __tablename__ = "employees"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    employee_id = Column(String, unique=True, index=True)
    phone = Column(String)
    hire_date = Column(Date)
    department = Column(String)
    position = Column(String)
    salary = Column(Numeric(10, 2))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", backref="employee")
    reviews_received = relationship("Review", back_populates="employee", foreign_keys="Review.employee_id")
    timesheets = relationship("Timesheet", back_populates="employee")
    leave_requests = relationship("LeaveRequest", back_populates="employee")
    documents = relationship("Document", back_populates="employee")
    benefits = relationship("EmployeeBenefit", back_populates="employee")

    # Relationships
    timesheets = relationship("Timesheet", back_populates="employee")
    leave_requests = relationship("LeaveRequest", back_populates="employee")
    documents = relationship("Document", back_populates="employee")
    reviews = relationship("Review", back_populates="employee")
    benefits = relationship("EmployeeBenefit", back_populates="employee")

class Timesheet(Base):
    __tablename__ = "timesheets"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"))
    date = Column(Date)
    hours_worked = Column(Numeric(4, 2))
    status = Column(String)  # submitted, approved, rejected
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employee = relationship("Employee", back_populates="timesheets")

class LeaveRequest(Base):
    __tablename__ = "leave_requests"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"))
    start_date = Column(Date)
    end_date = Column(Date)
    leave_type = Column(String)  # vacation, sick, personal
    status = Column(String)  # pending, approved, rejected
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employee = relationship("Employee", back_populates="leave_requests")

class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"))
    title = Column(String)
    file_path = Column(String)
    document_type = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employee = relationship("Employee", back_populates="documents")

class Review(Base):
    __tablename__ = "reviews"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"))
    review_date = Column(Date)
    reviewer_id = Column(Integer, ForeignKey("employees.id"))
    performance_score = Column(Integer)
    comments = Column(Text)
    status = Column(String)  # draft, submitted, acknowledged
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employee = relationship(
        "Employee",
        foreign_keys=[employee_id],
        back_populates="reviews_received"
    )
    reviewer = relationship(
        "Employee",
        foreign_keys=[reviewer_id],
        backref="reviews_given"
    )

class Job(Base):
    __tablename__ = "jobs"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String)
    department = Column(String)
    description = Column(Text)
    requirements = Column(Text)
    status = Column(String)  # open, closed, draft
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    applications = relationship("JobApplication", back_populates="job")

class JobApplication(Base):
    __tablename__ = "job_applications"

    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(Integer, ForeignKey("jobs.id"))
    applicant_name = Column(String)
    applicant_email = Column(String)
    resume_path = Column(String)
    status = Column(String)  # submitted, reviewed, interviewing, offered, rejected
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    job = relationship("Job", back_populates="applications")

class Benefit(Base):
    __tablename__ = "benefits"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    description = Column(Text)
    type = Column(String)  # health, dental, vision, 401k, etc.
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employee_benefits = relationship("EmployeeBenefit", back_populates="benefit")

class EmployeeBenefit(Base):
    __tablename__ = "employee_benefits"

    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"))
    benefit_id = Column(Integer, ForeignKey("benefits.id"))
    enrollment_date = Column(Date)
    status = Column(String)  # active, pending, terminated
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employee = relationship("Employee", back_populates="benefits")
    benefit = relationship("Benefit", back_populates="employee_benefits")
